<Window x:Class="ApartmanYonetimSistemi.Views.LoginView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Apartman Yönetim <PERSON> - <PERSON>" Height="450" Width="400"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Ba<PERSON>lık -->
        <TextBlock Text="Apartman Yönetim Sistemi" FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,20,0,30"/>
        
        <!-- Giriş Formu -->
        <StackPanel Grid.Row="1" VerticalAlignment="Center">
            <TextBlock Text="E-posta:" Margin="0,0,0,5"/>
            <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}" 
                     Padding="5" Margin="0,0,0,15"/>
            
            <TextBlock Text="Şifre:" Margin="0,0,0,5"/>
            <PasswordBox x:Name="PasswordBox" Padding="5" Margin="0,0,0,20"/>
            
            <Button Content="Giriş Yap" Command="{Binding LoginCommand}" 
                    Padding="10,5" Margin="0,0,0,15"/>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="Şifremi Unuttum" Command="{Binding ForgotPasswordCommand}" 
                        Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" 
                        Foreground="Blue" Margin="0,0,15,0"/>
                <Button Content="Kayıt Ol" Command="{Binding RegisterCommand}" 
                        Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" 
                        Foreground="Blue"/>
            </StackPanel>
        </StackPanel>
        
        <!-- Durum Mesajı -->
        <TextBlock Grid.Row="2" Text="{Binding StatusMessage}" 
                   Foreground="{Binding StatusColor}"
                   HorizontalAlignment="Center" Margin="0,20,0,0"/>
    </Grid>
</Window>