using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class LoginViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;
        
        [ObservableProperty]
        private string _email = string.Empty;
        
        [ObservableProperty]
        private string _statusMessage = string.Empty;
        
        [ObservableProperty]
        private Brush _statusColor = Brushes.Black;

        public LoginViewModel(FirebaseService firebaseService)
        {
            _firebaseService = firebaseService;
        }

        [RelayCommand]
        private async Task Login(object parameter)
        {
            try
            {
                var passwordBox = parameter as System.Windows.Controls.PasswordBox;
                if (passwordBox == null) return;
                
                string password = passwordBox.Password;
                
                if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(password))
                {
                    StatusMessage = "Lütfen e-posta ve şifre giriniz.";
                    StatusColor = Brushes.Red;
                    return;
                }
                
                StatusMessage = "Giriş yapılıyor...";
                StatusColor = Brushes.Black;
                
                var result = await _firebaseService.SignInWithEmailAndPasswordAsync(Email, password);
                
                if (result.Success)
                {
                    // Ana ekrana yönlendir
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        // MainWindow'u aç
                        // var mainWindow = new MainWindow();
                        // mainWindow.Show();
                        
                        // Giriş penceresini kapat
                        // Window.GetWindow(passwordBox).Close();
                    });
                }
                else
                {
                    StatusMessage = "Giriş başarısız: " + result.ErrorMessage;
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
        }

        [RelayCommand]
        private void ForgotPassword()
        {
            var forgotPasswordDialog = new ForgotPasswordDialog();
            forgotPasswordDialog.ShowDialog();
        }

        [RelayCommand]
        private void Register()
        {
            var registerDialog = new RegisterDialog();
            registerDialog.ShowDialog();
        }
    }
}