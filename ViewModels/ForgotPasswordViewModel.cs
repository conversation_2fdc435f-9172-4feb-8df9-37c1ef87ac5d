using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class ForgotPasswordViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;
        
        [ObservableProperty]
        private string _email = string.Empty;
        
        [ObservableProperty]
        private string _statusMessage = string.Empty;
        
        [ObservableProperty]
        private Brush _statusColor = Brushes.Black;

        public ForgotPasswordViewModel(FirebaseService firebaseService)
        {
            _firebaseService = firebaseService;
        }

        [RelayCommand]
        private async Task ResetPassword()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(Email))
                {
                    StatusMessage = "Lütfen e-posta adresinizi giriniz.";
                    StatusColor = Brushes.Red;
                    return;
                }
                
                StatusMessage = "Sıfırlama bağlantısı gönderiliyor...";
                StatusColor = Brushes.Black;
                
                var result = await _firebaseService.SendPasswordResetEmailAsync(Email);
                
                if (result.Success)
                {
                    StatusMessage = "Sıfırlama bağlantısı gönderildi. Lütfen e-postanızı kontrol edin.";
                    StatusColor = Brushes.Green;
                    
                    // Kısa bir süre sonra pencereyi kapat
                    await Task.Delay(3000);
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        Window.GetWindow(this).Close();
                    });
                }
                else
                {
                    StatusMessage = "İşlem başarısız: " + result.ErrorMessage;
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
        }
    }
}