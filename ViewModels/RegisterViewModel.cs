using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class RegisterViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;
        
        [ObservableProperty]
        private string _email = string.Empty;
        
        [ObservableProperty]
        private string _statusMessage = string.Empty;
        
        [ObservableProperty]
        private Brush _statusColor = Brushes.Black;

        public RegisterViewModel(FirebaseService firebaseService)
        {
            _firebaseService = firebaseService;
        }

        [RelayCommand]
        private async Task Register(object parameter)
        {
            try
            {
                var passwordBox = parameter as System.Windows.Controls.PasswordBox;
                if (passwordBox == null) return;
                
                string password = passwordBox.Password;
                
                if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(password))
                {
                    StatusMessage = "Lütfen e-posta ve şifre giriniz.";
                    StatusColor = Brushes.Red;
                    return;
                }
                
                // Şifre doğrulama kontrolü yapılabilir
                
                StatusMessage = "Kayıt yapılıyor...";
                StatusColor = Brushes.Black;
                
                var result = await _firebaseService.CreateUserWithEmailAndPasswordAsync(Email, password);
                
                if (result.Success)
                {
                    StatusMessage = "Kayıt başarılı! Giriş yapabilirsiniz.";
                    StatusColor = Brushes.Green;
                    
                    // Kısa bir süre sonra pencereyi kapat
                    await Task.Delay(2000);
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        Window.GetWindow(passwordBox).Close();
                    });
                }
                else
                {
                    StatusMessage = "Kayıt başarısız: " + result.ErrorMessage;
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
        }
    }
}