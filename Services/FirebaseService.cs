using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace ApartmanYonetimSistemi.Services
{
    public class FirebaseService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private const string BaseUrl = "https://identitytoolkit.googleapis.com/v1/accounts";

        public FirebaseService()
        {
            _httpClient = new HttpClient();
            _apiKey = "AIzaSyCgpczDXCbIakUBZB-KxPLaEWKLRgrtj2M";
        }

        public async Task<AuthResult> SignInWithEmailAndPasswordAsync(string email, string password)
        {
            var requestData = new
            {
                email,
                password,
                returnSecureToken = true
            };

            return await SendAuthRequestAsync(requestData, "signInWithPassword");
        }

        public async Task<AuthResult> CreateUserWithEmailAndPasswordAsync(string email, string password)
        {
            var requestData = new
            {
                email,
                password,
                returnSecureToken = true
            };

            return await SendAuthRequestAsync(requestData, "signUp");
        }

        public async Task<AuthResult> SendPasswordResetEmailAsync(string email)
        {
            var requestData = new
            {
                email,
                requestType = "PASSWORD_RESET"
            };

            return await SendAuthRequestAsync(requestData, "sendOobCode");
        }

        private async Task<AuthResult> SendAuthRequestAsync(object data, string endpoint)
        {
            try
            {
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync(
                    $"{BaseUrl}:{endpoint}?key={_apiKey}", content);
                
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var authResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    return new AuthResult
                    {
                        Success = true,
                        IdToken = authResponse.GetProperty("idToken").GetString(),
                        Email = authResponse.TryGetProperty("email", out var email) ? email.GetString() : null,
                        LocalId = authResponse.GetProperty("localId").GetString()
                    };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    var error = errorResponse.GetProperty("error");
                    var message = error.GetProperty("message").GetString();
                    
                    return new AuthResult
                    {
                        Success = false,
                        ErrorMessage = GetFriendlyErrorMessage(message)
                    };
                }
            }
            catch (Exception ex)
            {
                return new AuthResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private string GetFriendlyErrorMessage(string errorCode)
        {
            return errorCode switch
            {
                "EMAIL_NOT_FOUND" => "Bu e-posta adresi ile kayıtlı kullanıcı bulunamadı.",
                "INVALID_PASSWORD" => "Geçersiz şifre.",
                "USER_DISABLED" => "Bu kullanıcı hesabı devre dışı bırakılmış.",
                "EMAIL_EXISTS" => "Bu e-posta adresi zaten kullanılıyor.",
                "OPERATION_NOT_ALLOWED" => "Bu işlem şu anda izin verilmiyor.",
                "TOO_MANY_ATTEMPTS_TRY_LATER" => "Çok fazla başarısız giriş denemesi. Lütfen daha sonra tekrar deneyin.",
                _ => $"Bir hata oluştu: {errorCode}"
            };
        }
    }

    public class AuthResult
    {
        public bool Success { get; set; }
        public string IdToken { get; set; }
        public string Email { get; set; }
        public string LocalId { get; set; }
        public string ErrorMessage { get; set; }
    }
}